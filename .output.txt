import React, { useState } from 'react';
import {
  Box,
  Typography,
  Button,
  Paper,
  Grid,
  Alert,
  CircularProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Link,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  ListItemButton,
  Divider,
  Card,
  CardContent,
  CardActions,
  Tooltip
} from '@mui/material';
import {
  Upload as UploadIcon,
  Download as DownloadIcon,
  FileUpload as FileUploadIcon,
  FileDownload as FileDownloadIcon,
  Description as DescriptionIcon,
  ImportExport as ImportExportIcon,
  Add as AddIcon,
  GetApp as GetAppIcon
} from '@mui/icons-material';
import excelService from '../../services/excelService';
const GestioneExcel = ({ cantiereId, onSuccess, onError }) => {
  const [loading, setLoading] = useState(false);
  const [selectedOption, setSelectedOption] = useState(null);
  const [openDialog, setOpenDialog] = useState(false);
  const [dialogType, setDialogType] = useState('');
  const [filePath, setFilePath] = useState('');
  const [downloadLink, setDownloadLink] = useState('');
  const [fileInput, setFileInput] = useState(null);
  // Gestisce la selezione di un'opzione dal menu
  const handleOptionSelect = (option) => {
    setSelectedOption(option);
    if (option === 'importaCavi') {
      setDialogType('importaCavi');
      setOpenDialog(true);
    } else if (option === 'importaParcoBobine') {
      setDialogType('importaParcoBobine');
      setOpenDialog(true);
    } else if (option === 'creaTemplateCavi') {
      handleCreaTemplateCavi();
    } else if (option === 'creaTemplateParcoBobine') {
      handleCreaTemplateParcoBobine();
    } else if (option === 'esportaCavi') {
      handleEsportaCavi();
    } else if (option === 'esportaParcoBobine') {
      handleEsportaParcoBobine();
    }
  };
  // Gestisce la chiusura del dialog
  const handleCloseDialog = () => {
    setOpenDialog(false);
    setFilePath('');
    setFileInput(null);
  };
  // Gestisce la creazione del template Excel per cavi
  const handleCreaTemplateCavi = async () => {
    try {
      setLoading(true);
      const response = await excelService.createCaviTemplate();
      // Il download ÃƒÂ¨ giÃƒÂ  stato avviato automaticamente dal servizio
      onSuccess('Template Excel per cavi creato e download avviato! Controlla la cartella Download del tuo browser.');
      // Opzionalmente, mostra ancora il link per un download manuale
      if (response.file_url) {
        setDownloadLink(response.file_url);
        setDialogType('downloadTemplate');
        setOpenDialog(true);
      }
    } catch (error) {
      onError('Errore nella creazione del template Excel per cavi');
      console.error('Errore nella creazione del template Excel per cavi:', error);
    } finally {
      setLoading(false);
    }
  };
  // Gestisce la creazione del template Excel per parco bobine
  const handleCreaTemplateParcoBobine = async () => {
    try {
      setLoading(true);
      const response = await excelService.createParcoBobineTemplate();
      // Il download ÃƒÂ¨ giÃƒÂ  stato avviato automaticamente dal servizio
      onSuccess('Template Excel per parco bobine creato e download avviato! Controlla la cartella Download del tuo browser.');
      // Opzionalmente, mostra ancora il link per un download manuale
      if (response.file_url) {
        setDownloadLink(response.file_url);
        setDialogType('downloadTemplate');
        setOpenDialog(true);
      }
    } catch (error) {
      onError('Errore nella creazione del template Excel per parco bobine');
      console.error('Errore nella creazione del template Excel per parco bobine:', error);
    } finally {
      setLoading(false);
    }
  };
  // Gestisce l'esportazione dei cavi in Excel
  const handleEsportaCavi = async () => {
    try {
      setLoading(true);
      const response = await excelService.exportCavi(cantiereId);
      // Il download ÃƒÂ¨ giÃƒÂ  stato avviato automaticamente dal servizio
      onSuccess('Cavi esportati con successo e download avviato! Controlla la cartella Download del tuo browser.');
      // Opzionalmente, mostra ancora il link per un download manuale
      if (response.file_url) {
        setDownloadLink(response.file_url);
        setDialogType('downloadExport');
        setOpenDialog(true);
      }
    } catch (error) {
      onError('Errore nell\'esportazione dei cavi');
      console.error('Errore nell\'esportazione dei cavi:', error);
    } finally {
      setLoading(false);
    }
  };
  // Gestisce l'esportazione del parco bobine in Excel
  const handleEsportaParcoBobine = async () => {
    try {
      setLoading(true);
      const response = await excelService.exportParcoBobine(cantiereId);
      // Il download ÃƒÂ¨ giÃƒÂ  stato avviato automaticamente dal servizio
      onSuccess('Parco bobine esportato con successo e download avviato! Controlla la cartella Download del tuo browser.');
      // Opzionalmente, mostra ancora il link per un download manuale
      if (response.file_url) {
        setDownloadLink(response.file_url);
        setDialogType('downloadExport');
        setOpenDialog(true);
      }
    } catch (error) {
      onError('Errore nell\'esportazione del parco bobine');
      console.error('Errore nell\'esportazione del parco bobine:', error);
    } finally {
      setLoading(false);
    }
  };
  // Gestisce l'importazione dei cavi da Excel
  const handleImportaCavi = async () => {
    try {
      if (!fileInput) {
        onError('Seleziona un file Excel da importare');
        return;
      }
      setLoading(true);
      const formData = new FormData();
      formData.append('file', fileInput);
      await excelService.importCavi(cantiereId, formData);
      onSuccess('Cavi importati con successo');
      handleCloseDialog();
    } catch (error) {
      onError('Errore nell\'importazione dei cavi: ' + (error.message || 'Errore sconosciuto'));
      console.error('Errore nell\'importazione dei cavi:', error);
    } finally {
      setLoading(false);
    }
  };
  // Gestisce l'importazione del parco bobine da Excel
  const handleImportaParcoBobine = async () => {
    try {
      if (!fileInput) {
        onError('Seleziona un file Excel da importare');
        return;
      }
      setLoading(true);
      const formData = new FormData();
      formData.append('file', fileInput);
      await excelService.importParcoBobine(cantiereId, formData);
      onSuccess('Parco bobine importato con successo');
      handleCloseDialog();
    } catch (error) {
      onError('Errore nell\'importazione del parco bobine: ' + (error.message || 'Errore sconosciuto'));
      console.error('Errore nell\'importazione del parco bobine:', error);
    } finally {
      setLoading(false);
    }
  };
  // Gestisce il cambio del file selezionato
  const handleFileChange = (e) => {
    setFileInput(e.target.files[0]);
  };
  // Renderizza il dialog in base al tipo
  const renderDialog = () => {
    if (dialogType === 'importaCavi' || dialogType === 'importaParcoBobine') {
      return (
        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="sm" fullWidth>
          <DialogTitle>
            {dialogType === 'importaCavi' ? 'Importa Cavi da Excel' : 'Importa Parco Bobine da Excel'}
          </DialogTitle>
          <DialogContent>
            <Box sx={{ mt: 2 }}>
              <Alert severity="info" sx={{ mb: 2 }}>
                Seleziona un file Excel da importare. Assicurati che il formato sia corretto.
              </Alert>
              <TextField
                type="file"
                fullWidth
                variant="outlined"
                InputLabelProps={{ shrink: true }}
                onChange={handleFileChange}
                inputProps={{ accept: '.xlsx, .xls' }}
              />
            </Box>
          </DialogContent>
          <DialogActions>
            <Button onClick={handleCloseDialog}>Annulla</Button>
            <Button
              onClick={dialogType === 'importaCavi' ? handleImportaCavi : handleImportaParcoBobine}
              disabled={loading || !fileInput}
              startIcon={loading ? <CircularProgress size={20} /> : <UploadIcon />}
            >
              Importa
            </Button>
          </DialogActions>
        </Dialog>
      );
    } else if (dialogType === 'downloadTemplate' || dialogType === 'downloadExport') {
      return (
        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="sm" fullWidth>
          <DialogTitle>
            {dialogType === 'downloadTemplate' ? 'Template Excel Creato' : 'Esportazione Completata'}
          </DialogTitle>
          <DialogContent>
            <Box sx={{ mt: 2 }}>
              <Alert severity="success" sx={{ mb: 2 }}>
                {dialogType === 'downloadTemplate'
                  ? 'Il template Excel ÃƒÂ¨ stato creato con successo e il download ÃƒÂ¨ stato avviato automaticamente.'
                  : 'L\'esportazione ÃƒÂ¨ stata completata con successo e il download ÃƒÂ¨ stato avviato automaticamente.'}
              </Alert>
              <Typography variant="body1" gutterBottom>
                Il file dovrebbe essere scaricato automaticamente nella cartella Download del tuo browser.
              </Typography>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                Se il download non ÃƒÂ¨ partito automaticamente, clicca sul link sottostante:
              </Typography>
              <Link
                href={downloadLink}
                target="_blank"
                rel="noopener noreferrer"
                download
                sx={{ display: 'flex', alignItems: 'center', mt: 1 }}
              >
                <DownloadIcon sx={{ mr: 1 }} />
                Scarica file Excel
              </Link>
            </Box>
          </DialogContent>
          <DialogActions>
            <Button onClick={handleCloseDialog}>Chiudi</Button>
          </DialogActions>
        </Dialog>
      );
    }
    return null;
  };
  return (
    <Box>
      {downloadLink ? (
        <Paper sx={{ p: 3, minHeight: '200px', display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center' }}>
          <Typography variant="h6" gutterBottom>
            Download pronto
          </Typography>
          <Link href={downloadLink} download target="_blank" rel="noopener">
            <Button variant="contained" color="primary" startIcon={<DownloadIcon />}>
              Scarica file
            </Button>
          </Link>
        </Paper>
      ) : (
        <Box>
          {loading ? (
            <Paper sx={{ p: 3, minHeight: '300px', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
              <Box sx={{ textAlign: 'center' }}>
                <Typography variant="h6" gutterBottom>
                  {selectedOption === 'importaCavi' && 'Importa cavi da Excel'}
                  {selectedOption === 'importaParcoBobine' && 'Importa parco bobine da Excel'}
                  {selectedOption === 'creaTemplateCavi' && 'Crea Template Excel per cavi'}
                  {selectedOption === 'creaTemplateParcoBobine' && 'Crea Template Excel per parco bobine'}
                  {selectedOption === 'esportaCavi' && 'Esporta cavi in Excel'}
                  {selectedOption === 'esportaParcoBobine' && 'Esporta parco bobine in Excel'}
                </Typography>
                <CircularProgress sx={{ mt: 2 }} />
              </Box>
            </Paper>
          ) : (
            <Box>
              <Typography variant="h6" gutterBottom>
                Gestione Excel
              </Typography>
              <Typography variant="body2" paragraph>
                Seleziona un'operazione da eseguire:
              </Typography>
              <Grid container spacing={3}>
                {/* Importazione */}
                <Grid item xs={12}>
                  <Typography variant="subtitle1" gutterBottom sx={{ mt: 2, fontWeight: 'bold' }}>
                    Importazione
                  </Typography>
                  <Divider sx={{ mb: 2 }} />
                </Grid>
                <Grid item xs={12} sm={6} md={4}>
                  <Card variant="outlined">
                    <CardContent>
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                        <ImportExportIcon color="primary" sx={{ mr: 1 }} />
                        <Typography variant="h6">Importa Cavi</Typography>
                      </Box>
                      <Typography variant="body2" color="text.secondary">
                        Importa cavi da un file Excel. Il file deve essere nel formato corretto.
                      </Typography>
                    </CardContent>
                    <CardActions>
                      <Button
                        size="small"
                        startIcon={<FileUploadIcon />}
                        onClick={() => handleOptionSelect('importaCavi')}
                      >
                        Importa
                      </Button>
                    </CardActions>
                  </Card>
                </Grid>
                <Grid item xs={12} sm={6} md={4}>
                  <Card variant="outlined">
                    <CardContent>
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                        <ImportExportIcon color="primary" sx={{ mr: 1 }} />
                        <Typography variant="h6">Importa Bobine</Typography>
                      </Box>
                      <Typography variant="body2" color="text.secondary">
                        Importa parco bobine da un file Excel. Il file deve essere nel formato corretto.
                      </Typography>
                    </CardContent>
                    <CardActions>
                      <Button
                        size="small"
                        startIcon={<FileUploadIcon />}
                        onClick={() => handleOptionSelect('importaParcoBobine')}
                      >
                        Importa
                      </Button>
                    </CardActions>
                  </Card>
                </Grid>
                {/* Templates */}
                <Grid item xs={12}>
                  <Typography variant="subtitle1" gutterBottom sx={{ mt: 2, fontWeight: 'bold' }}>
                    Templates
                  </Typography>
                  <Divider sx={{ mb: 2 }} />
                </Grid>
                <Grid item xs={12} sm={6} md={4}>
                  <Card variant="outlined">
                    <CardContent>
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                        <DescriptionIcon color="primary" sx={{ mr: 1 }} />
                        <Typography variant="h6">Template Cavi</Typography>
                      </Box>
                      <Typography variant="body2" color="text.secondary">
                        Crea un template Excel per l'importazione dei cavi.
                      </Typography>
                    </CardContent>
                    <CardActions>
                      <Button
                        size="small"
                        startIcon={<GetAppIcon />}
                        onClick={() => handleOptionSelect('creaTemplateCavi')}
                      >
                        Crea Template
                      </Button>
                    </CardActions>
                  </Card>
                </Grid>
                <Grid item xs={12} sm={6} md={4}>
                  <Card variant="outlined">
                    <CardContent>
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                        <DescriptionIcon color="primary" sx={{ mr: 1 }} />
                        <Typography variant="h6">Template Bobine</Typography>
                      </Box>
                      <Typography variant="body2" color="text.secondary">
                        Crea un template Excel per l'importazione del parco bobine.
                      </Typography>
                    </CardContent>
                    <CardActions>
                      <Button
                        size="small"
                        startIcon={<GetAppIcon />}
                        onClick={() => handleOptionSelect('creaTemplateParcoBobine')}
                      >
                        Crea Template
                      </Button>
                    </CardActions>
                  </Card>
                </Grid>
                {/* Esportazione */}
                <Grid item xs={12}>
                  <Typography variant="subtitle1" gutterBottom sx={{ mt: 2, fontWeight: 'bold' }}>
                    Esportazione
                  </Typography>
                  <Divider sx={{ mb: 2 }} />
                </Grid>
                <Grid item xs={12} sm={6} md={4}>
                  <Card variant="outlined">
                    <CardContent>
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                        <FileDownloadIcon color="primary" sx={{ mr: 1 }} />
                        <Typography variant="h6">Esporta Cavi</Typography>
                      </Box>
                      <Typography variant="body2" color="text.secondary">
                        Esporta tutti i cavi del cantiere in un file Excel.
                      </Typography>
                    </CardContent>
                    <CardActions>
                      <Button
                        size="small"
                        startIcon={<DownloadIcon />}
                        onClick={() => handleOptionSelect('esportaCavi')}
                      >
                        Esporta
                      </Button>
                    </CardActions>
                  </Card>
                </Grid>
                <Grid item xs={12} sm={6} md={4}>
                  <Card variant="outlined">
                    <CardContent>
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                        <FileDownloadIcon color="primary" sx={{ mr: 1 }} />
                        <Typography variant="h6">Esporta Bobine</Typography>
                      </Box>
                      <Typography variant="body2" color="text.secondary">
                        Esporta tutto il parco bobine in un file Excel.
                      </Typography>
                    </CardContent>
                    <CardActions>
                      <Button
                        size="small"
                        startIcon={<DownloadIcon />}
                        onClick={() => handleOptionSelect('esportaParcoBobine')}
                      >
                        Esporta
                      </Button>
                    </CardActions>
                  </Card>
                </Grid>
              </Grid>
            </Box>
          )}
        </Box>
      )}
      {renderDialog()}
    </Box>
  );
};
export default GestioneExcel;